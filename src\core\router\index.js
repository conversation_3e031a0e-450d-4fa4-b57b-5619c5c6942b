import Vue from 'vue'
import Router from 'vue-router'
import Home from '@/core/views/Home'
import Intro from 'biz/views/Intro/Intro'
import NotFound from '@/core/views/404'
import api from '@/core/http/api'
import store from '@/core/store'
import settings from 'biz/http/settings'
import Cookies from 'js-cookie'
import { getIFramePath, getIFrameUrl } from '@/core/utils/iframe'
import Watermark from '@/utils/watermark'
import { toggleClass } from '@/core/utils/theme'
import { singleMessage } from '@/core/utils/singleMessage'
import { logout, setTimer } from '@/core/utils/logout'
import { putAllDictCache, intervalCache } from '@/core/utils/tabCache'
import { getRefreshToken } from '@/core/utils/token'

let cookieName = config.cookieName
let fristLoading = true
const routerPush = Router.prototype.push
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch((error) => error)
}

Vue.use(Router)

const routerReplace = Router.prototype.replace
Router.prototype.replace = function replace(location) {
  return routerReplace.call(this, location).catch((error) => error)
}

const Rts = [
  {
    path: '/',
    // name: '主页',
    component: Home,
    redirect: settings.bizHomeRedirect ? settings.bizHomeRedirect : '/intro',
    children: [
      {
        path: 'intro',
        name: '首页',
        component: Intro,
        meta: {
          icon: 'fa fa-home fa-lg',
          index: 0
        }
      }
    ]
  },
  {
    path: '/login',
    name: '登录',
    component: (resolve) => require([`@/core/views/Login/${config.loginBg}/Login`], resolve)
  } /* ,
{
  path: '*',
  name: '404',
  component: NotFound
} */
]

const createRouter = () => {
  let routes = {
    // mode: 'history', // require service support
    // base: process.env.BASE_URL,
    // scrollBehavior: () => ({ y: 0 }),
    routes: []
  }
  return new Router(routes)
}
const router = createRouter()
router.$addRoutes = function (params) {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher
  router.addRoutes(params)
}
console.log('12', router.options.routes)

router.options.routes = router.options.routes.concat(Rts)
if (settings.bizRouters) {
  router.options.routes = router.options.routes.concat(settings.bizRouters)
}
// 添加路由白名单
// let dynamicRoutes = addWhiteRoutes()
// router.options.routes = router.options.routes.concat(dynamicRoutes)
router.addRoutes(router.options.routes)

router.beforeEach(async (to, from, next) => {
  /* if(to.matched.length === 0) {
    next({name: '404'})
  } */
  const href = window.location.href
  let url = new URL(href)
  // 遍历url.searchParams
  for (let [key, value] of url.searchParams) {
    if (!to.query[key]) {
      to.query[key] = value
    }
  }
  if (Object.keys(to.query).indexOf('login-state') > -1) {
    // 白名单只需请求一次SSO，记录下从SSO返回的参数login-state
    sessionStorage.setItem('whitePageLoginRqState', to.query['login-state'])
  }

  // 登录界面登录成功之后，会把用户信息保存在会话
  // 存在时间为会话生命周期，页面关闭即失效。
  let user = sessionStorage.getItem('user')

  let jsonUser = user ? JSON.parse(user) : undefined
  if (fristLoading) {
    await loadConfig()
  }
  // 2022年12月20日 18:35:20 增加条件 to.path !== '/login' 访问登录页面无需请求用户信息
  if (
    fristLoading &&
    cookieName &&
    typeof Cookies.get(cookieName) !== 'undefined' &&
    to.path !== '/login' &&
    user
  ) {
    let token = JSON.parse(Cookies.get(cookieName))
    await getUserInfo(token.accessToken, token.refreshToken)
    user = sessionStorage.getItem('user')
    store.commit('menuRouteLoaded', false)
    fristLoading = false
  }
  if (to.path === '/login') {
    document.querySelectorAll('.v-modal').forEach((node) => node.remove())
    // 登录页面清除水印
    Watermark.removeWaterMark()
    // 如果是访问登录界面，如果用户会话信息存在，代表已登录过，跳转到主页
    // 并且用户是密码已更新状态（在isOnUims下有效）
    if ((!config.isOnUims && user) || (config.isOnUims && jsonUser && jsonUser.pwdState === '1')) {
      if (to.query && to.query.redirectPath) {
        next({ path: to.query.redirectPath })
      } else {
        next({ path: '/' })
      }
      store.commit('setShowRightPanel', config.showRightPanel)
    } else {
      if (config.sso.onoff) {
        if (typeof to.params.redirectPath !== 'undefined') {
          // window.location.href = config.sso.url + '?redirectPath=' + to.params.redirectPath
          window.location.href = to.params.redirectPath
        } else {
          // 门户系统点击登录没有带参数的问题
          api.authority.getState().then((res) => {
            let state = res.data
            let redirectUri = encodeURIComponent(url.href.replace(/([?&])code=[^&]+(&|$)/, ''))
            // if(config.sso.isCompatible) {
            //   // 老统一用户
            //   window.location.href = config.sso.logoutUrl + '?fsc=' + config.systemCode + '&redirectPath=' + encodeURIComponent(window.location.href) + '&msg=no login' // 返回单点登录SSO页面
            // } else {
            // 新统一用户
            // if(top !== self) {
            //   window.top.postMessage(JSON.stringify({BaseUIType: 'logout', data: {}}), '*');
            // } else {
            window.location.href =
              config.sso.url +
              '?system_code=' +
              config.systemCode +
              '&client_id=' +
              config.client_id +
              '&response_type=code&scope=all&state=' +
              state +
              '&redirect_uri=' +
              redirectUri // 返回单点登录SSO页面
            // }
            // }
          })
        }
      } else {
        next()
      }
    }
  } else if (settings.bizRouters.map((i) => i.path).indexOf(to.path) !== -1) {
    if (config.sso.onoff && (to.query.ticket || to.query.code || to.query.token)) {
      let ticket = to.query.ticket
      if (!ticket && to.query.code) {
        ticket = to.query.code
      }
      // 在单点登录跳转时，若带了token，则保存token信息
      let data = null
      if (config.isOnUims) {
        data = ticket
          ? { code: ticket, state: to.query.state, url: location.href }
          : { token: to.query.token }
      } else {
        data = { ticket: ticket, state: to.query.state }
      }
      api.authority
        .getUserInfoByToken(data)
        .then((res) => {
          if (res.redirectPath) {
            location.replace(res.redirectPath)
            return
          }
          Cookies.set(cookieName, {
            accessToken: res.access_token,
            refreshToken: res.refresh_token
          })
          // 依据接入指南返回user_name 前端兼容复制个userName
          res.userName = res.user_name
          sessionStorage.setItem('user', JSON.stringify(res)) // 保存用户到本地会话
          sessionStorage.setItem('resetTime', Date.now() + res.expires_in * 1000) // 保存token过期时间到本地会话
          store.commit('setCurrentUser', res)
          setTimer()
          // next({path: to.path.indexOf("/login") !== -1 ? '/' : to.path})
          if (to.path.indexOf('/login') !== -1) {
            next({ path: '/' })
          } else {
            // 带参数
            let { code, state, ticket, token, ...params } = { ...to.query }
            next({ path: to.path, query: params })
          }
        })
        .catch((e) => {
          if (e.code === '401') {
            Cookies.remove(cookieName)
          }
        })
    } else {
      if (to.query && to.query.hlf) {
        // 从其他系统跳过来带了用户存在的标记，走单点登录
        if (config.isOnUims) {
          api.authority.getState().then((res) => {
            let state = res.data
            let redirectUri = encodeURIComponent(url.href.replace(/([?&])code=[^&]+(&|$)/, ''))
            // if(config.sso.isCompatible) {
            //   // 老统一用户
            //   window.location.href = config.sso.logoutUrl + '?fsc=' + config.systemCode + '&redirectPath=' + encodeURIComponent(window.location.href) + '&msg=no login' // 返回单点登录SSO页面
            // } else {
            // 新统一用户
            // if(top !== self) {
            //   window.top.postMessage(JSON.stringify({BaseUIType: 'logout', data: {}}), '*');
            // } else {
            window.location.href =
              config.sso.url +
              '?system_code=' +
              config.systemCode +
              '&client_id=' +
              config.client_id +
              '&response_type=code&scope=all&state=' +
              state +
              '&redirect_uri=' +
              redirectUri // 返回单点登录SSO页面
            // }
            // }
          })
        }
      } else {
        if (user) {
          // 在白名单页面刷新页面，user信息丢失的问题
          store.commit('setCurrentUser', JSON.parse(user))

          // 白名单页面如果用户已登录，也需要获取菜单按钮的资源权限
          if (!store.state.app.menuRouteLoaded) {
            let jsonUser = JSON.parse(user)
            await addDynamicAndFuncRoutes(jsonUser.userName)
          }
        } else {
          // 检查是否为新窗口环境，尝试从父窗口获取用户信息
          let openerUser = null
          try {
            if (window.opener && window.opener.sessionStorage) {
              openerUser = window.opener.sessionStorage.getItem('user')
            }
          } catch (e) {
            // 跨域或其他安全限制，忽略错误
            console.warn('无法从父窗口获取用户信息:', e)
          }

          if (openerUser) {
            // 从父窗口获取到用户信息，设置到当前窗口
            sessionStorage.setItem('user', openerUser)
            let jsonUser = JSON.parse(openerUser)
            store.commit('setCurrentUser', jsonUser)

            // 获取菜单按钮的资源权限
            if (!store.state.app.menuRouteLoaded) {
              await addDynamicAndFuncRoutes(jsonUser.userName)
            }
          } else if (!sessionStorage.getItem('whitePageLoginRqState') && config.sso.onoff) {
            // 白名单首次请求SSO时，需带参数login-required=false跳转SSO
            const param = '&login-required=false'
            const redirectUri = encodeURIComponent(url.href.replace(/([?&])code=[^&]+(&|$)/, ''))
            // 将sso.url最后面的oauth/authorize移除掉
            let ssoUrl = config.sso.url
            if (ssoUrl.indexOf('oauth/authorize') > -1) {
              ssoUrl = ssoUrl.replace('/oauth/authorize/', '').replace('/oauth/authorize', '')
            }
            // 形成地址http://ip:端口/#/?system_code=igdp&client_id=app&login-required=false&redirect_uri=http%3A%2F%2F10.8.4.64%3A8082%2Figdp%2F%23%2Fnest%2Fwork%2Findex
            ssoUrl = ssoUrl + '/#/'
            window.location.href =
              ssoUrl +
              '?system_code=' +
              config.systemCode +
              '&client_id=' +
              config.client_id +
              param +
              '&redirect_uri=' +
              redirectUri // 返回单点登录SSO页面
          }
        }
      }
      next()
    }
    store.commit('setShowRightPanel', false)
  } else {
    // 2022年12月8日 14:39:49增加或条件 (config.isOnUims && user.pwdState === '1') 用户没有重置密码返回登录页面
    if (
      !user ||
      typeof Cookies.get(cookieName) === 'undefined' ||
      (config.isOnUims && jsonUser && jsonUser.pwdState === '1')
    ) {
      sessionStorage.removeItem('user')
      sessionStorage.removeItem('resetTime')
      sessionStorage.removeItem('store')
      // 如果访问非登录界面，且户会话信息不存在，代表未登录，则跳转到登录界面
      if (config.sso.onoff) {
        // 若开启了单点登录，则走单点登录
        if (to.query.token || to.query.ticket || to.query.code) {
          let ticket = to.query.ticket
          if (!ticket && to.query.code) {
            ticket = to.query.code
          }
          let data = null
          if (config.isOnUims) {
            data = ticket
              ? { code: ticket, state: to.query.state, url: location.href }
              : { token: to.query.token }
          } else {
            data = { ticket: ticket, state: to.query.state }
          }
          api.authority
            .getUserInfoByToken(data)
            .then((res) => {
              if (res.redirectPath) {
                location.replace(res.redirectPath)
                return
              }
              Cookies.set(cookieName, {
                accessToken: res.access_token,
                refreshToken: res.refresh_token
              })
              res.userName = res.user_name
              sessionStorage.setItem('user', JSON.stringify(res)) // 保存用户到本地会话
              sessionStorage.setItem('resetTime', Date.now() + res.expires_in * 1000) // 保存token过期时间到本地会话
              store.commit('setCurrentUser', res)
              setTimer()
              // router.push(typeof to.query.redirectPath === 'undefined' || to.query.redirectPath.indexOf("/login") !== -1 ? '/' : to.query.redirectPath) // 登录成功并跳转
              // router.push(typeof to.query.redirectPath === 'undefined' || to.query.redirectPath.indexOf("/login") !== -1 ? '/' : to.query.redirectPath) // 登录成功并跳转
              store.commit('setShowRightPanel', config.showRightPanel)
              // next({path: to.path.indexOf("/login") !== -1 ? '/' : to.path})
              if (config.isOnUims) {
                if (to.path.indexOf('/login') !== -1) {
                  next({ path: '/' })
                } else {
                  // 带参数
                  let { code, state, ticket, token, ...params } = { ...to.query }
                  next({ path: to.path, query: params })
                }
              } else {
                let query = {}
                for (let key in to.query) {
                  if (key !== 'ticket' && key !== 'code' && key !== 'token' && key !== 'state') {
                    query[key] = to.query[key]
                  }
                }
                next({ path: to.path.indexOf('/login') !== -1 ? '/' : to.path, query: query })
              }
            })
            .catch((e) => {
              if (e.code === '401') {
                Cookies.remove(cookieName)
                if (config.isOnUims) {
                  api.authority.getState().then((res) => {
                    let state = res.data
                    let redirectUri = encodeURIComponent(
                      url.href.replace(/([?&])code=[^&]+(&|$)/, '')
                    )
                    // if(config.sso.isCompatible) {
                    //   // 老统一用户
                    //   window.location.href = config.sso.logoutUrl + '?fsc=' + config.systemCode + '&redirectPath=' + encodeURIComponent(window.location.href) + '&msg=no login' // 返回单点登录SSO页面
                    // } else {
                    // 新统一用户
                    // if(top !== self) {
                    //   window.top.postMessage(JSON.stringify({BaseUIType: 'logout', data: {}}), '*');
                    // } else {
                    window.location.href =
                      config.sso.url +
                      '?system_code=' +
                      config.systemCode +
                      '&client_id=' +
                      config.client_id +
                      '&response_type=code&scope=all&state=' +
                      state +
                      '&redirect_uri=' +
                      redirectUri // 返回单点登录SSO页面
                    // }
                    // }
                  })
                }
              }
            })
        } else if (to.query && to.query.hlf) {
          // 从其他系统跳过来带了用户存在的标记，走单点登录
          api.authority.getState().then((res) => {
            let state = res.data
            let redirectUri = encodeURIComponent(url.href.replace(/([?&])code=[^&]+(&|$)/, ''))
            // if(config.sso.isCompatible) {
            //   // 老统一用户
            //   window.location.href = config.sso.logoutUrl + '?fsc=' + config.systemCode + '&redirectPath=' + encodeURIComponent(window.location.href) + '&msg=no login' // 返回单点登录SSO页面
            // } else {
            // 新统一用户
            // if(top !== self) {
            //   window.top.postMessage(JSON.stringify({BaseUIType: 'logout', data: {}}), '*');
            // } else {
            window.location.href =
              config.sso.url +
              '?system_code=' +
              config.systemCode +
              '&client_id=' +
              config.client_id +
              '&response_type=code&scope=all&state=' +
              state +
              '&redirect_uri=' +
              redirectUri // 返回单点登录SSO页面
            // }
            // }
          })
        } else {
          // 正常进入页面
          to.query.redirectPath = to.path
          Cookies.remove(cookieName)
          // sessionStorage.removeItem(cookieName);
          if (config.isOnUims) {
            api.authority.getState().then((res) => {
              let state = res.data
              let redirectUri = encodeURIComponent(url.href.replace(/([?&])code=[^&]+(&|$)/, ''))
              // if(config.sso.isCompatible) {
              //   // 老统一用户
              //   window.location.href = config.sso.logoutUrl + '?fsc=' + config.systemCode + '&redirectPath=' + encodeURIComponent(window.location.href) + '&msg=no login' // 返回单点登录SSO页面
              // } else {
              // 新统一用户
              // if(top !== self) {
              //   window.top.postMessage(JSON.stringify({BaseUIType: 'logout', data: {}}), '*');
              // } else {
              window.location.href =
                config.sso.url +
                '?system_code=' +
                config.systemCode +
                '&client_id=' +
                config.client_id +
                '&response_type=code&scope=all&state=' +
                state +
                '&redirect_uri=' +
                redirectUri // 返回单点登录SSO页面
              // }
              // }
            })
          } else {
            // if(top !== self) {
            //   window.top.postMessage(JSON.stringify({BaseUIType: 'logout', data: {}}), '*');
            // } else {
            window.location.href =
              config.sso.url + '?redirectPath=' + encodeURIComponent(window.location.href)
            // }
          }
        }
        if (to.query['x-space-id']) {
          // sessionStorage.setItem('spaceId', to.query['x-space-id'])
          let spaceIdCookies = Cookies.get('spaceId')
          if (!spaceIdCookies) {
            spaceIdCookies = {}
          } else {
            spaceIdCookies = JSON.parse(Cookies.get('spaceId'))
          }
          // spaceIdCookies[user.userName] = to.query['x-space-id'];
          Cookies.set('spaceId', JSON.stringify(spaceIdCookies))
        }
      } else {
        to.query.redirectPath = to.path
        next({ path: '/login', query: to.query }) // 否则走本地登录
      }
    } else {
      // 加载动态菜单和路由
      let shadowDiv = document.createElement('div')
      shadowDiv.style.position = 'fixed'
      shadowDiv.style.left = 0
      shadowDiv.style.right = 0
      shadowDiv.style.top = 0
      shadowDiv.style.bottom = 0
      shadowDiv.style.zIndex = 999999999
      shadowDiv.className = 'shadowbox'
      document.body.appendChild(shadowDiv)
      store.commit('setShowRightPanel', config.showRightPanel)
      addDynamicMenuAndRoutes(JSON.parse(user), to, from)
      if (
        config.sso.onoff &&
        ((config.ticketModel && (to.query.ticket || to.query.code)) ||
          (!config.ticketModel && to.query.token))
      ) {
        if (config.isOnUims) {
          next({ path: to.path })
        } else {
          let query = {}
          for (let key in to.query) {
            if (key !== 'ticket' && key !== 'code' && key !== 'token' && key !== 'state') {
              query[key] = to.query[key]
            }
          }
          next({ path: to.path, query: query })
        }
      } else {
        next()
      }
    }
  }
  // 用户存在且不在白名单页面加水印
  if (jsonUser) {
    if (checkInWaterMarkWhiteList(to.path)) {
      Watermark.set((jsonUser.userName || jsonUser.user_name) + '(' + jsonUser.realName + ')')
    } else {
      Watermark.removeWaterMark()
    }
  }
})
router.beforeResolve((to, from, next) => {
  if (to.name === '404' && config.no404 && sessionStorage.getItem('user')) {
    singleMessage({ message: '您访问的页面不存在或无权访问。', type: 'error' })
    next({ path: '/' })
  } else {
    next()
  }
})

async function loadConfig() {
  // if(!store.state.app.configs.initFlag) {
  config.initFlag = true
  store.commit('setConfigs', config)
  await api.authority
    .getConfig()
    .then((res) => {
      if (res.data) {
        let configs
        if (typeof res.data === 'string') {
          configs = JSON.parse(config)
        } else {
          configs = Object.assign({}, config)
        }
        copyConfig(res.data, configs)
        if (Object.keys(configs).indexOf('openWatermark') !== -1) {
          config.openWatermark = configs.openWatermark
        }
        store.commit('setConfigs', configs)
      }
    })
    .catch(() => {})
  // }
}

function copyConfig(resData, configData) {
  if (typeof resData === 'object') {
    for (let data in resData) {
      if (typeof resData[data] === 'object') {
        if (configData[data]) {
          for (let cData in resData[data]) {
            copyConfig(resData[data][cData], configData[data][cData])
          }
        } else {
          configData[data] = resData[data]
        }
      } else {
        configData[data] = resData[data]
      }
    }
  } else {
    configData = resData
  }
}

// 获取皮肤设置
// eslint-disable-next-line
function getThemeOnServer() {
  // 获取服务器上的皮肤设置
  api.authority.getTheme().then((res) => {
    const serviceTheme = res.data
    if (serviceTheme && serviceTheme.theme) {
      // 判断系统设置的皮肤范围是否有这个皮肤（部分系统可能只限定了某些皮肤config.js配置文件theme配置项进行控制）如果服务器设置的皮肤在配置文件中不存在，则置空使用默认的
      let themeRange = config.theme.thumbnails
      if (themeRange.filter((t) => t.name === serviceTheme.theme.skin).length === 0) {
        serviceTheme.theme = undefined
      }
    }
    if (!serviceTheme || !serviceTheme.theme) {
      // 默认配置文件中的皮肤
      const data = {
        thumbnailImg: config.css,
        thumbnailPicture: config.theme.thumbnails.filter((t) => t.name === config.css)[0]
          .pictures[0].name
      }
      localStorage.setItem('thumbnail', JSON.stringify(data))
      store.commit('setThumbnail', data)
      return
    }
    const data = {
      thumbnailImg: serviceTheme.theme.skin,
      thumbnailPicture: serviceTheme.theme.picture
    }
    if (config.theme.version >= serviceTheme.version) {
      // 当前版本 >= 服务器上的版本，以服务器上的为准
      localStorage.setItem('thumbnail', JSON.stringify(data))
      store.commit('setThumbnail', data)
      toggleClass(document.body, serviceTheme.theme.skin)
    } else {
      // 当前版本 < 服务器上的版本，需要做进一步判断
      let isFind = config.theme.thumbnails.find((o) => {
        return (
          o.name === serviceTheme.theme.skin &&
          o.pictures.find((n) => {
            return n.name === serviceTheme.theme.picture
          })
        )
      })
      if (typeof isFind !== 'undefined') {
        // 若服务器上是高版本低设置，以服务器上的为准
        localStorage.setItem('thumbnail', JSON.stringify(data))
        store.commit('setThumbnail', data)
        toggleClass(document.body, serviceTheme.theme.skin)
      }
    }
  })
}

// 水印白名单检查
function checkInWaterMarkWhiteList(path) {
  const warterMarkWhiteList = settings.warterMarkWhiteList
  if (warterMarkWhiteList && warterMarkWhiteList.length > 0) {
    if (!path) {
      return true
    }
    for (let i = 0; i < warterMarkWhiteList.length; i++) {
      if (warterMarkWhiteList[i].indexOf('/*') === warterMarkWhiteList[i].length - 2) {
        if (
          path.indexOf(
            warterMarkWhiteList[i].substring(0, warterMarkWhiteList[i].indexOf('/*'))
          ) === 0
        ) {
          return false
        }
      } else {
        if (warterMarkWhiteList[i] === path) {
          return false
        }
      }
    }
  }
  return true
}

/**
 * 加载动态菜单和路由
 */
async function addDynamicMenuAndRoutes(user, to, from) {
  // 处理IFrame嵌套页面
  handleIFrameUrl(to.path)
  if (!store.state.user.currentUser.userName) {
    store.commit('setCurrentUser', JSON.parse(sessionStorage.getItem('user')))
  }
  if (store.state.app.menuRouteLoaded) {
    /**
     * 动态菜单和路由已经存在，不需要重新加载菜单
     * 并且判断URL中的空间ID是否和当前空间ID一致，若不一致，优先以URL中的空间ID为准
     */
    let spaceIdCookies = Cookies.get('spaceId')
    if (!spaceIdCookies) {
      spaceIdCookies = {}
    } else {
      spaceIdCookies = JSON.parse(Cookies.get('spaceId'))
    }
    let curSpaceId = spaceIdCookies[user.userName]
    // 判断是否有新控件ID的权限
    if (
      to.query['x-space-id'] &&
      curSpaceId &&
      to.query['x-space-id'] !== curSpaceId &&
      store.state.system.system.tsysSpaces &&
      store.state.system.system.tsysSpaces.filter((i) => i.spaceId === to.query['x-space-id'])
        .length > 0
    ) {
      store.commit('setSpaceId', to.query['x-space-id'])
      spaceIdCookies[user.userName] = to.query['x-space-id']
      Cookies.set('spaceId', JSON.stringify(spaceIdCookies))
    }
    let shadowBox = document.getElementsByClassName('shadowbox')
    for (var i = 0; i < shadowBox.length; i++) {
      shadowBox[0].remove()
    }
    // console.log('动态菜单和路由已经存在.')
    return
  }
  // 获取皮肤设置
  // await getThemeOnServer();
  await getSystemInfo(user, to.query['x-space-id'])
  if (config.initAllDict) {
    // 新增功能去一次新加载字典缓存到页面
    initAllDict()
  }
  let shadowBox = document.getElementsByClassName('shadowbox')
  for (let i = 0; i < shadowBox.length; i++) {
    shadowBox[0].remove()
  }
}
async function getSystemInfo(user, spaceId) {
  await api.authority
    .getSystemInfo({ sysCode: config.systemCode })
    .then((res) => {
      store.commit('setSystem', res.data)
      let spaceIdCookies = Cookies.get('spaceId')
      if (!spaceIdCookies) {
        spaceIdCookies = {}
      } else {
        spaceIdCookies = JSON.parse(Cookies.get('spaceId'))
      }
      if (res.data.tsysSpaces && res.data.tsysSpaces.length > 0) {
        // 从缓存中获取spaceId， 如果缓存中没有spaceId或者没有与其匹配的spaceId就默认取第一条空间的空间ID
        // let lastSpaceId = sessionStorage.getItem('spaceId')
        let lastSpaceId = spaceIdCookies[user.userName]

        if (spaceId && res.data.tsysSpaces.filter((i) => i.spaceId === spaceId).length > 0) {
          // 优先使用url里面的空间ID
          store.commit('setSpaceId', spaceId)
          spaceIdCookies[user.userName] = spaceId
        } else {
          if (lastSpaceId === '||empty||') {
            store.commit('setSpaceId', '||empty||')
            // sessionStorage.setItem('spaceId', '')
            spaceIdCookies[user.userName] = '||empty||'
          } else {
            let spaceModel = res.data.tsysSpaces.filter((i) => i.spaceId === lastSpaceId)
            if (!lastSpaceId || spaceModel.length < 1) {
              store.commit('setSpaceId', res.data.tsysSpaces[0].spaceId)
              // sessionStorage.setItem('spaceId', res.data.tsysSpaces[0].spaceId)
              spaceIdCookies[user.userName] = res.data.tsysSpaces[0].spaceId
            } else {
              store.commit('setSpaceId', lastSpaceId)
              // sessionStorage.setItem('spaceId', lastSpaceId)
              spaceIdCookies[user.userName] = lastSpaceId
            }
          }
        }
      } else {
        store.commit('setSpaceId', '')
        // sessionStorage.setItem('spaceId', '')
        spaceIdCookies[user.userName] = ''
      }
      Cookies.set('spaceId', JSON.stringify(spaceIdCookies))
      store.commit('setPrivModel', res.data.spaceSystemFlag)
      if (
        res.data.spaceSystemFlag === '2' &&
        res.data.tsysSpaces &&
        res.data.tsysSpaces.length > 0
      ) {
        res.data.tsysSpaces.unshift({
          spaceId: '||empty||',
          spaceName: '非空间'
        })
      }
      store.commit('setSpacesList', res.data.tsysSpaces)
      // store.commit('setUnionUsers', res.data)
    })
    .catch((e) => {
      // 接口异常程序中断，后面的遮罩不会移出，这里要catch一下
    })
  // api.authority.findUnionUser().then(res => {
  api.authority
    .findUnionUser({ userName: user.userName })
    .then((res) => {
      store.commit('setUnionUsers', res.data)
    })
    .catch((e) => {
      // 接口异常程序中断，后面的遮罩不会移出，这里要catch一下
    })
  await addDynamicAndFuncRoutes(user.userName)
}
function initAllDict() {
  api.dict.getDictionaries().then((res) => {
    if (res.all) {
      let dicts = {}
      for (let i = 0; i < res.all.length; i++) {
        let dict = res.all[i]
        if (dicts[dict.dictType]) {
          dicts[dict.dictType][dict.code] = dict.name
        } else {
          dicts[dict.dictType] = {}
          dicts[dict.dictType][dict.code] = dict.name
        }
      }
      putAllDictCache(dicts)
    }
  })
  intervalCache()
}
async function getUserInfo(accessToken, refreshToken) {
  if (config.isOnUims) {
    // 同统一用户不能走中台的方式刷新token，因为刷新token会把原accessToken作废，在非互斥登录模式下会互相作废对方的accessToken
    // await api.authority.getUserInfoByToken({token: accessToken}).then((res) => {
    //   if (res.redirectPath) {
    //     location.replace(res.redirectPath + '?token=' + res.access_token)
    //   }
    //   Cookies.set(cookieName, {'accessToken': res.access_token, 'refreshToken': res.refresh_token})
    //   sessionStorage.setItem('user', JSON.stringify(res)) // 保存用户到本地会话
    //   sessionStorage.setItem('resetTime', Date.now() + res.expires_in * 1000) // 保存token过期时间到本地会话
    //   store.commit('setCurrentUser', res)
    // })
  } else {
    await getRefreshToken(refreshToken)
      .then((res) => {
        if (res.redirectPath) {
          location.replace(res.redirectPath + '?code=' + res.code)
          return
        }
        Cookies.set(cookieName, { accessToken: res.access_token, refreshToken: res.refresh_token })
        res.userName = res.user_name
        sessionStorage.setItem('user', JSON.stringify(res)) // 保存用户到本地会话
        sessionStorage.setItem('resetTime', Date.now() + res.expires_in * 1000) // 保存token过期时间到本地会话
        store.commit('setCurrentUser', res)
        setTimer()
      })
      .catch((e) => {
        if (e.code === '401') {
          Cookies.remove(cookieName)
        }
      })
  }
}
async function addDynamicAndFuncRoutes(userName) {
  // api.authority.findAuthority({'systemCode': config.systemCode}).then(res => {
  await api.authority
    .findAuthority({ systemCode: config.systemCode, userName: userName })
    .then((res) => {
      if (res.code === '400') {
        singleMessage({ message: res.message || '用户信息失效，请重新登录！', type: 'error' })
        // 重定向到登录页面
        logout()
      }
      // 保存加载状态
      store.commit('menuRouteLoaded', true)
      setTimer()
      let arr = [
        {
          path: 'intro',
          name: '首页',
          component: Intro,
          meta: {
            icon: 'fa fa-home fa-lg',
            index: 0
          }
        }
      ]
      let routes = addDynamicRoutes(res.data)
      // console.log('routes', routes);
      // 添加动态路由
      arr = arr.concat(routes.dynamicRoutes)
      router.options.routes[0].children = arr
      // 添加共用路由（用户菜单）
      let sharedRoutes = routes.sharedRoutes
        .map((item) => {
          return item.data.resourceType === '0'
            ? null
            : item.url.substring(0, item.url.indexOf('#'))
        })
        .filter((item) => item)
      sharedRoutes = [...new Set(sharedRoutes)]
      let sRs = sharedRoutes.map((item) => {
        return {
          path: item + '/:id?',
          redirect: (to) => {
            if (to.hash) {
              return {
                name: routes.sharedRoutes.filter(
                  (o) => o.url.substring(o.url.indexOf('#')) === to.hash
                )[0].name
              }
            }
          }
        }
      })
      router.options.routes[0].children = router.options.routes[0].children.concat(sRs)
      // 添加共用路由（功能菜单）
      let sharedRoutes0 = routes.sharedRoutes
        .map((item) => {
          return item.data.resourceType === '0'
            ? item.url.substring(0, item.url.indexOf('#'))
            : null
        })
        .filter((item) => item)
      sharedRoutes0 = [...new Set(sharedRoutes0)]
      let sRs0 = sharedRoutes0.map((item) => {
        return {
          path: '/' + item + '/:id?',
          redirect: (to) => {
            if (to.hash) {
              return {
                name: routes.sharedRoutes.filter(
                  (o) => o.url.substring(o.url.indexOf('#')) === to.hash
                )[0].name
              }
            }
          }
        }
      })
      // 添加功能菜单路由
      router.options.routes = router.options.routes.filter(
        (r) => !(r.meta && r.meta.resourceType && r.meta.resourceType === '0')
      )
      router.options.routes = router.options.routes.concat(routes.funcRoutes).concat(sRs0)
      // 添加404
      if (router.options.routes.filter((r) => r.path === '*').length === 0) {
        router.options.routes.push({
          path: '*',
          name: '404',
          component: NotFound
        })
      }
      router.$addRoutes(router.options.routes)
      // console.log(router)
      // 保存菜单树
      let menus = JSON.parse(JSON.stringify(res.data))
      menus = addDynamicMenus(menus, '1')
      // 单页面模式，首页放在菜单树上
      if (config.page_mode === 'full') {
        menus.unshift({
          fullName: '首页',
          icon: '/icons/svg/' + (config.homeIcon ? config.homeIcon : 'homepage'),
          menuLevel: 0,
          name: '首页',
          orderNo: 0,
          parentId: null,
          url: 'intro',
          data: {
            resourceName: '首页',
            resourceType: '1',
            uri: 'intro'
          }
        })
      }
      store.commit('setNavTree', menus)
      // 保存功能菜单树
      let funcMenus = JSON.parse(JSON.stringify(res.data))
      funcMenus = addDynamicMenus(funcMenus, '0')
      store.commit('setFuncTree', funcMenus)
      // 保存用户按钮权限
      menus = JSON.parse(JSON.stringify(res.data))
      let permissions = addDynamicPermissions(menus)
      store.commit('setPerms', permissions)
      store.commit('setShowRightPanel', config.showRightPanel)
    })
    .catch((res) => {})
}

/**
 * 添加动态(菜单)路由
 * @param {*} menuList 菜单列表
 * @param {*} routes 递归创建的动态(菜单)路由
 */
function addDynamicRoutes(
  menuList = [],
  routes = { dynamicRoutes: [], funcRoutes: [], sharedRoutes: [] }
) {
  var temp = []
  for (var i = 0; i < menuList.length; i++) {
    menuList[i].id = menuList[i].data.resourceId
    menuList[i].name = menuList[i].data.resourceName
    menuList[i].url = menuList[i].data.uri
    menuList[i].icon = menuList[i].data.icon
    if (!menuList[i].data.parentId) {
      menuList[i].fullName = menuList[i].data.resourceName
    }
    if (menuList[i].children && menuList[i].children.length >= 1) {
      menuList[i].children.forEach((item) => {
        item.fullName =
          (menuList[i].fullName ? menuList[i].fullName : menuList[i].data.resourceName) +
          '/' +
          item.data.resourceName
      })
      temp = temp.concat(menuList[i].children)
    }
    if (menuList[i].url && /\S/.test(menuList[i].url)) {
      // menuList[i].url = menuList[i].url.replace(/^\//, '')
      menuList[i].url = menuList[i].url.replace(/^\/views\//, '')
      menuList[i].url =
        menuList[i].url.indexOf('/') === 0
          ? menuList[i].url.substring(1, menuList[i].url.length)
          : menuList[i].url
      // 创建路由配置
      var route = {
        path: menuList[i].data.resourceType === '0' ? '/' + menuList[i].url : menuList[i].url,
        component: null,
        name: config.page_mode !== 'full' ? menuList[i].name : menuList[i].fullName,
        meta: {
          icon: menuList[i].icon,
          index: menuList[i].id,
          resourceType: menuList[i].data.resourceType,
          hash: menuList[i].url.substring(
            0,
            menuList[i].url.indexOf('#') === -1
              ? menuList[i].url.length
              : menuList[i].url.indexOf('#')
          )
        }
      }
      let path = getIFramePath(menuList[i].url)
      if (path) {
        // 如果是嵌套页面, 通过iframe展示
        route['path'] = path
        route['component'] = (resolve) => require([`@/core/views/IFrame/IFrame`], resolve)
        route['iframeComponent'] = route['component']
        route['meta']['isIframe'] = true
        // 存储嵌套页面路由路径和访问URL
        let url = getIFrameUrl(menuList[i].url)
        let iFrameUrl = { path: path, url: url }
        store.commit('addIFrameUrl', iFrameUrl)
      } else {
        try {
          // 根据菜单URL动态加载vue组件，这里要求vue组件须按照url路径存储
          // 如url="sys/user"，则组件路径应是"@/views/sys/user.vue",否则组件加载不到
          /* let array = menuList[i].url.split('/')
          let url = ''
          for (let i = 0; i < array.length; i++) {
            url += array[i].substring(0, 1).toUpperCase() + array[i].substring(1) + '/'
          }
          url = url.substring(0, url.length - 1) */
          let url = menuList[i].url
          /**
           * 共用组件路由
           */
          if (url.indexOf('#') > -1) {
            url = url.substring(url.indexOf('#') + 1)
            // views 开头替换成空
            url = url.replace(/^\/views\//, '')
            // 没有views / 开头替换成空
            url = url.replace(/^\//, '')
            // console.log(url);
            route['path'] = route['path'].substring(0, route['path'].indexOf('#'))
            menuList[i].url = route['path'].replace(/^\//, '')
          }
          if (menuList[i].data.isFrame === '1') {
            route['component'] = (resolve) => require([`@/core/views/${url}`], resolve)
          } else {
            if (url !== '/') {
              route['component'] = (resolve) => require([`biz/views/${url}`], resolve)
            }
          }
        } catch (e) {}
      }
      if (menuList[i].data.resourceType === '0') {
        if (menuList[i].data.openMode === '0') {
          // 设置在tab中打开
          routes.dynamicRoutes.push(route)
        } else {
          routes.funcRoutes.push(route)
        }
      } else {
        routes.dynamicRoutes.push(route)
      }
    }
  }
  if (temp.length >= 1) {
    addDynamicRoutes(temp, routes)
  } else {
    /* console.log('动态路由加载……')
    console.log(routes)
    console.log('动态路由加载完成……') */
  }
  return routes
}

/**
 * 添加动态(菜单)路由
 * @param {*} menuList 菜单列表
 */
function addDynamicMenus(menuList = [], resourceType = '1', fg = 0) {
  var temp = []
  menuList = menuList.filter((m) => m.data.resourceType === resourceType)
  for (var i = 0; i < menuList.length; i++) {
    if (menuList[i].data.resourceType === resourceType) {
      if (menuList[i].children && menuList[i].children.length >= 1) {
        menuList[i].children = menuList[i].children.filter(
          (c) => c.data.resourceType === resourceType
        )
        menuList[i].children = addDynamicMenus(menuList[i].children, resourceType, fg + 1)
      }
      menuList[i].menuLevel = fg // 菜单的层级
      temp = temp.concat(menuList[i])
    }
  }
  return temp
}
/**
 * 添加动态(用户按钮)权限
 * @param {*} menuList 菜单列表
 * @param {*} permissions 递归创建的动态按钮权限
 */
function addDynamicPermissions(menuList = [], permissions = []) {
  for (var i = 0; i < menuList.length; i++) {
    if (menuList[i].data.resourceType === '2') {
      permissions.push(menuList[i].data.resourceCode)
    }
    if (menuList[i].children && menuList[i].children.length >= 1) {
      addDynamicPermissions(menuList[i].children, permissions)
    }
  }
  return permissions
}

/**
 * 清空路由
 */
export function resetRouter() {
  store.commit('setCurrentUser', {})
  store.commit('setNavTree', [])
  store.commit('setPerms', [])
  store.commit(
    'updateMainTabs',
    store.state.tab.mainTabs.filter((item) => item.name === '首页')
  )
  store.commit('menuRouteLoaded', false)
  store.commit('setShowRightPanel', false)
  const newRouter = createRouter()
  // router.matcher = newRouter.matcher // reset router
  // router.options.routes[0].children = []
  // router.addRoutes(router.options.routes)
  newRouter.options.routes = Rts
  router.$addRoutes(newRouter.options.routes)
}

/**
 * 切换动态菜单和路由
 */
export function resetDynamicMenuAndRoutes(username) {
  // 更新当前用户信息
  api.authority.switchUser({ username: username }).then((res) => {
    resetRouter()
    Cookies.set(cookieName, { accessToken: res.access_token, refreshToken: res.refresh_token })
    // Cookies.set(cookieName, res.access_token, { sameSite: 'strict', domatin: document.domain + ':' + window.location.port })
    res.userName = res.user_name
    sessionStorage.setItem('user', JSON.stringify(res))
    sessionStorage.setItem('resetTime', Date.now() + res.expires_in * 1000) // 保存token过期时间到本地会话
    store.commit('setCurrentUser', res)
    setTimer()
    // 更新联合用户信息
    // api.authority.findUnionUser().then(res => {
    api.authority.findUnionUser({ userName: username }).then((res) => {
      store.commit('setUnionUsers', res.data)
    })
    addDynamicAndFuncRoutes(username)
    // 切换用户，移除上个用户的水印
    Watermark.removeWaterMark()
    router.push('/') // 切换成功，跳转到主页
  })
}

/**
 * 添加白名单路由
 * @param {*} menuList 菜单列表
 * @param {*} routes 递归创建的动态(菜单)路由
 */
// eslint-disable-next-line
function addWhiteRoutes(routes = []) {
  let [...whiteList] = settings.whiteList
  for (var i = 0; i < whiteList.length; i++) {
    if (whiteList[i] && /\S/.test(whiteList[i])) {
      // 创建路由配置
      var route = {
        path: whiteList[i],
        component: null,
        name: whiteList[i].replace(/^\//, '')
      }
      try {
        // 根据菜单URL动态加载vue组件，这里要求vue组件须按照url路径存储
        // 如url="sys/user"，则组件路径应是"@/views/sys/user.vue",否则组件加载不到
        // let array = whiteList[i].replace(/^\//, '').split('/')
        // let url = ''
        // for (let i = 0; i < array.length; i++) {
        //   url += array[i].substring(0, 1).toUpperCase() + array[i].substring(1) + '/'
        // }
        // url = url.substring(0, url.length - 1)
        let url = whiteList[i]
        if (url.indexOf('/') === 0) {
          url = url.substring(1)
        }
        route['component'] = (resolve) => require([`biz/views/${url}`], resolve)
      } catch (e) {}
      routes.push(route)
    }
  }
  return routes
}

/**
 * 处理IFrame嵌套页面
 */
function handleIFrameUrl(path) {
  if (path === null) {
    return
  }
  // 嵌套页面，保存iframeUrl到store，供IFrame组件读取展示
  let url = path
  let length = store.state.iframe.iframeUrls.length
  for (let i = 0; i < length; i++) {
    let iframe = store.state.iframe.iframeUrls[i]
    if (path.endsWith(iframe.path)) {
      url = iframe.url
      store.commit('setIFrameUrl', url)
      break
    }
  }
}

export default router
